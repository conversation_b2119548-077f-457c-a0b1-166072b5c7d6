﻿@using System.Globalization
@inherits LayoutComponentBase
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="page @GetBodyClass()">
    <header>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white">
            <div class="container">
                <a class="navbar-brand" href="/">
                    @if (IsArabic())
                    {
                        <text>نظام تقييم الموظفين</text>
                    }
                    else
                    {
                        <text>Employee Rating System</text>
                    }
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                @if (IsArabic())
                                {
                                    <text>لوحة التحكم</text>
                                }
                                else
                                {
                                    <text>Dashboard</text>
                                }
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/departments">
                                @if (IsArabic())
                                {
                                    <text>الأقسام</text>
                                }
                                else
                                {
                                    <text>Departments</text>
                                }
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/evaluations">
                                @if (IsArabic())
                                {
                                    <text>التقييمات</text>
                                }
                                else
                                {
                                    <text>Evaluations</text>
                                }
                            </a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/login">
                                @if (IsArabic())
                                {
                                    <text>تسجيل الدخول</text>
                                }
                                else
                                {
                                    <text>Login</text>
                                }
                            </a>
                        </li>

                        <!-- Language Switcher -->
                        <li class="nav-item">
                            <div class="language-switcher">
                                @if (IsArabic())
                                {
                                    <button class="btn btn-outline-primary btn-sm fw-bold" style="min-width: 40px; border-radius: 20px;"
                                            title="Switch to English" @onclick="@(() => SwitchLanguage("en"))">E</button>
                                }
                                else
                                {
                                    <button class="btn btn-outline-primary btn-sm fw-bold" style="min-width: 40px; border-radius: 20px;"
                                            title="التبديل إلى العربية" @onclick="@(() => SwitchLanguage("ar"))">ع</button>
                                }
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mt-4">
        @Body
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        @if (IsArabic())
                        {
                            <text>© 2025 نظام تقييم الموظفين. جميع الحقوق محفوظة.</text>
                        }
                        else
                        {
                            <text>© 2025 Employee Rating System. All rights reserved.</text>
                        }
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        @if (IsArabic())
                        {
                            <text>الإصدار 2.0 - النسخة المحسنة للمؤسسات</text>
                        }
                        else
                        {
                            <text>Version 2.0 - Enhanced Enterprise Edition</text>
                        }
                    </p>
                </div>
            </div>
        </div>
    </footer>
</div>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    private string GetCurrentLanguage()
    {
        return CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
    }

    private string GetBodyClass()
    {
        return IsArabic() ? "rtl-layout" : "";
    }

    private bool IsArabic()
    {
        return GetCurrentLanguage() == "ar";
    }

    private async Task SwitchLanguage(string culture)
    {
        await JSRuntime.InvokeVoidAsync("eval", $"document.cookie = \"culture={culture}; path=/\"; location.reload();");
    }
}
